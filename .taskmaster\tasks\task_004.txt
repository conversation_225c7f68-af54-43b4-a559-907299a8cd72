# Task ID: 4
# Title: 基础马丁格尔逻辑 (Basic Martingale Logic)
# Status: pending
# Dependencies: None
# Priority: high
# Description: 实现核心的马丁格尔交易策略，包括首次开仓、加仓条件判断和手数计算
# Details:
1. 实现CheckEntry()函数，判断首次开仓的市场条件
2. 实现CalculateLotSize()函数，根据加仓倍数计算新订单手数
3. 实现CheckGrid()函数，判断网格加仓的时机和条件
4. 支持买入模式(TradeDirection=0)的马丁格尔策略
5. 支持卖出模式(TradeDirection=1)的马丁格尔策略
6. 支持双向交易模式(TradeDirection=2)，同时处理买入和卖出
7. 实现加仓级别跟踪，记录当前加仓次数和级别

# Test Strategy:
回测不同市场条件，验证马丁格尔逻辑的正确性
