//+------------------------------------------------------------------+
//|                                        test_risk_control.mq4 |
//|                            Test script for risk control module |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test Risk Control"
#property version   "1.00"
#property strict
#property script_show_inputs

//+------------------------------------------------------------------+
//| 测试风险评估函数                                                    |
//+------------------------------------------------------------------+
bool TestCheckRisk()
{
   Print("=== 测试风险评估 ===");
   
   // 获取账户基本信息
   double balance = AccountBalance();
   double equity = AccountEquity();
   double freeMargin = AccountFreeMargin();
   double usedMargin = AccountMargin();
   
   Print("账户信息:");
   Print("- 余额: ", balance);
   Print("- 净值: ", equity);
   Print("- 可用保证金: ", freeMargin);
   Print("- 已用保证金: ", usedMargin);
   
   bool riskAcceptable = true;
   
   // 1. 检查回撤
   double drawdownPercent = 0;
   if(balance > 0)
   {
      drawdownPercent = ((balance - equity) / balance) * 100;
   }
   
   Print("当前回撤: ", drawdownPercent, "%");
   
   if(drawdownPercent > 30)
   {
      Print("警告: 回撤过大");
      riskAcceptable = false;
   }
   
   // 2. 检查保证金水平
   double marginLevel = 0;
   if(usedMargin > 0)
   {
      marginLevel = (equity / usedMargin) * 100;
      Print("保证金水平: ", marginLevel, "%");
      
      if(marginLevel < 200)
      {
         Print("警告: 保证金水平过低");
         riskAcceptable = false;
      }
   }
   else
   {
      Print("保证金水平: 无持仓");
   }
   
   return riskAcceptable;
}

//+------------------------------------------------------------------+
//| 测试保证金检查函数                                                  |
//+------------------------------------------------------------------+
bool TestMarginCheck(double lots)
{
   Print("=== 测试保证金检查 ===");
   
   double freeMargin = AccountFreeMargin();
   double requiredMargin = MarketInfo(Symbol(), MODE_MARGINREQUIRED) * lots;
   double safetyMargin = requiredMargin * 2;
   
   Print("保证金检查:");
   Print("- 测试手数: ", lots);
   Print("- 可用保证金: ", freeMargin);
   Print("- 所需保证金: ", requiredMargin);
   Print("- 安全保证金: ", safetyMargin);
   
   if(freeMargin < safetyMargin)
   {
      Print("结果: 保证金不足");
      return false;
   }
   
   Print("结果: 保证金充足");
   return true;
}

//+------------------------------------------------------------------+
//| 测试止损止盈价格计算                                                |
//+------------------------------------------------------------------+
void TestStopLossTakeProfit()
{
   Print("=== 测试止损止盈计算 ===");
   
   double currentBid = Bid;
   double currentAsk = Ask;
   int stopLossPoints = 50;
   int takeProfitPoints = 100;
   
   Print("当前价格:");
   Print("- Bid: ", currentBid);
   Print("- Ask: ", currentAsk);
   Print("- 止损点数: ", stopLossPoints);
   Print("- 止盈点数: ", takeProfitPoints);
   
   // 买单的止损止盈
   double buyStopLoss = currentAsk - stopLossPoints * Point;
   double buyTakeProfit = currentAsk + takeProfitPoints * Point;
   
   Print("买单止损止盈:");
   Print("- 开仓价: ", currentAsk);
   Print("- 止损价: ", buyStopLoss);
   Print("- 止盈价: ", buyTakeProfit);
   
   // 卖单的止损止盈
   double sellStopLoss = currentBid + stopLossPoints * Point;
   double sellTakeProfit = currentBid - takeProfitPoints * Point;
   
   Print("卖单止损止盈:");
   Print("- 开仓价: ", currentBid);
   Print("- 止损价: ", sellStopLoss);
   Print("- 止盈价: ", sellTakeProfit);
   
   // 验证最小距离
   double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
   Print("最小止损距离: ", minDistance / Point, " 点");
   
   bool buyStopValid = (currentBid - buyStopLoss) >= minDistance;
   bool buyTakeValid = (buyTakeProfit - currentAsk) >= minDistance;
   bool sellStopValid = (sellStopLoss - currentAsk) >= minDistance;
   bool sellTakeValid = (currentBid - sellTakeProfit) >= minDistance;
   
   Print("价格验证:");
   Print("- 买单止损有效: ", buyStopValid ? "是" : "否");
   Print("- 买单止盈有效: ", buyTakeValid ? "是" : "否");
   Print("- 卖单止损有效: ", sellStopValid ? "是" : "否");
   Print("- 卖单止盈有效: ", sellTakeValid ? "是" : "否");
}

//+------------------------------------------------------------------+
//| 测试资金保护机制                                                    |
//+------------------------------------------------------------------+
bool TestFundProtection()
{
   Print("=== 测试资金保护机制 ===");
   
   double balance = AccountBalance();
   double equity = AccountEquity();
   
   if(balance <= 0)
   {
      Print("错误: 账户余额异常");
      return false;
   }
   
   double drawdownPercent = ((balance - equity) / balance) * 100;
   double maxDrawdownPercent = 50.0;
   
   Print("资金保护检查:");
   Print("- 账户余额: ", balance);
   Print("- 账户净值: ", equity);
   Print("- 当前回撤: ", drawdownPercent, "%");
   Print("- 最大允许回撤: ", maxDrawdownPercent, "%");
   
   if(drawdownPercent >= maxDrawdownPercent)
   {
      Print("资金保护触发: 回撤过大");
      return false;
   }
   
   double minEquityPercent = 30.0;
   double minEquity = balance * (minEquityPercent / 100);
   
   Print("- 最小净值要求: ", minEquity, " (", minEquityPercent, "%)");
   
   if(equity <= minEquity)
   {
      Print("资金保护触发: 净值过低");
      return false;
   }
   
   Print("资金保护检查: 通过");
   return true;
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== 开始测试 Martingale EA 风险控制模块 ===");
   
   // 测试1: 基础风险评估
   Print("测试1: 基础风险评估");
   bool riskResult = TestCheckRisk();
   Print("风险评估结果: ", riskResult ? "可接受" : "风险过高");
   
   // 测试2: 保证金检查
   Print("测试2: 保证金检查");
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double testLots[] = {minLot, minLot * 2, minLot * 5, minLot * 10};
   
   for(int i = 0; i < ArraySize(testLots); i++)
   {
      bool marginResult = TestMarginCheck(testLots[i]);
      Print("手数 ", testLots[i], " 保证金检查: ", marginResult ? "通过" : "失败");
   }
   
   // 测试3: 止损止盈计算
   Print("测试3: 止损止盈计算");
   TestStopLossTakeProfit();
   
   // 测试4: 资金保护机制
   Print("测试4: 资金保护机制");
   bool fundResult = TestFundProtection();
   Print("资金保护结果: ", fundResult ? "安全" : "需要保护");
   
   // 测试5: 综合风险评估
   Print("测试5: 综合风险评估");
   
   bool overallRisk = riskResult && fundResult;
   Print("综合风险评估: ", overallRisk ? "可以交易" : "暂停交易");
   
   // 测试6: 模拟极端情况
   Print("测试6: 模拟极端情况分析");
   
   double balance = AccountBalance();
   double equity = AccountEquity();
   
   // 模拟不同回撤情况
   double[] simulatedEquity = {balance * 0.9, balance * 0.7, balance * 0.5, balance * 0.3};
   
   for(int j = 0; j < ArraySize(simulatedEquity); j++)
   {
      double simDrawdown = ((balance - simulatedEquity[j]) / balance) * 100;
      bool wouldTrigger = simDrawdown >= 50.0;
      
      Print("模拟净值 ", simulatedEquity[j], " (回撤 ", simDrawdown, "%): ", 
            wouldTrigger ? "触发保护" : "继续交易");
   }
   
   Print("=== 风险控制模块测试完成 ===");
   Print("结论: 风险控制机制", overallRisk ? "正常工作" : "需要关注");
}
//+------------------------------------------------------------------+
