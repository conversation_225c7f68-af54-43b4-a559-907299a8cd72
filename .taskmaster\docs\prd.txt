<context>
# 概述 (Overview)
马丁格尔策略外汇自动交易专家顾问（EA）是一个基于MQL4开发的智能交易系统，专为MetaTrader 4平台设计。该EA实现经典的马丁格尔交易策略，通过逐步加仓和网格交易来追求盈利。系统针对有经验的外汇交易者，提供完全可配置的参数设置，支持风险管理和安全机制，确保在实盘交易环境中的稳定运行。

该EA解决了手动执行马丁格尔策略的复杂性和人为错误问题，通过自动化交易逻辑，24小时监控市场并执行交易决策。系统具备完善的风险控制机制，包括最大加仓次数限制、止损保护和资金管理功能。

# 核心功能 (Core Features)
## 1. 马丁格尔交易逻辑
- 实现经典马丁格尔策略：亏损时加倍下注，盈利时重置
- 支持网格交易模式，在指定点差间隔进行加仓
- 可配置的加仓倍数和最大加仓次数
- 智能订单管理和持仓跟踪

## 2. 风险管理系统
- 最大加仓次数限制（默认7次）
- 止损和止盈点数设置
- 资金管理和手数计算
- 紧急停止交易机制

## 3. 参数配置系统
- 完全可配置的交易参数
- 实时参数验证和错误检查
- 参数保存和加载功能
- 用户友好的参数界面

## 4. 交易方向控制
- 支持仅买入、仅卖出或双向交易
- 市场趋势分析和方向判断
- 灵活的交易策略调整

# 用户体验 (User Experience)
## 用户画像
- 主要用户：有经验的外汇交易者
- 次要用户：量化交易爱好者和EA开发者
- 技能水平：中级到高级交易者，熟悉MT4平台操作

## 关键用户流程
1. EA安装和配置：用户在MT4中加载EA，设置交易参数
2. 策略启动：用户启用自动交易，EA开始监控市场
3. 交易执行：EA根据策略自动开仓、加仓和平仓
4. 监控管理：用户通过图表和日志监控EA运行状态
5. 参数调整：根据市场情况和交易结果调整策略参数

## UI/UX考虑
- 简洁直观的参数设置界面
- 清晰的状态指示和交易信息显示
- 详细的日志记录和错误提示
- 兼容MT4标准界面风格
</context>

<PRD>
# 技术架构 (Technical Architecture)
## 系统组件
### 1. 核心交易引擎 (Core Trading Engine)
- 订单管理模块：处理开仓、平仓、修改订单
- 马丁格尔逻辑模块：实现加仓倍数和网格策略
- 风险控制模块：监控持仓风险和资金安全
- 市场分析模块：价格监控和交易信号生成

### 2. 参数管理系统 (Parameter Management)
- 外部参数定义和验证
- 参数范围检查和默认值设置
- 运行时参数修改和保存

### 3. 日志和监控系统 (Logging & Monitoring)
- 交易日志记录
- 错误处理和异常捕获
- 性能监控和统计分析

## 数据模型
### 交易参数结构
```mql4
// 外部参数定义
extern double Lots = 0.01;              // 初始手数
extern double Multiplier = 2.0;         // 加仓倍数
extern int MaxOrders = 7;                // 最大加仓次数
extern int GridStep = 30;                // 加仓间距(点)
extern int TakeProfit = 50;              // 止盈点数
extern int StopLoss = 200;               // 止损点数
extern int TradeDirection = 2;           // 交易方向(0=买入,1=卖出,2=双向)
extern int MagicNumber = 123456;         // 魔术号码
```

### 订单跟踪结构
- 订单编号和类型
- 开仓价格和手数
- 加仓级别和盈亏状态
- 止盈止损设置

## API和集成
- MetaTrader 4 API集成
- MQL4标准函数库调用
- 经纪商服务器通信协议
- 实时价格数据接口

## 基础设施要求
- MetaTrader 4平台（版本4.00或更高）
- Windows操作系统支持
- 稳定的网络连接
- 足够的账户资金和保证金

# 开发路线图 (Development Roadmap)
## 第一阶段：MVP核心功能
### 基础框架搭建
- EA基本结构和初始化函数
- 外部参数定义和验证
- 基础错误处理机制

### 核心交易逻辑
- 简单的马丁格尔开仓逻辑
- 基础的加仓和平仓功能
- 订单管理和跟踪系统

### 基础风险控制
- 最大加仓次数限制
- 简单的止损止盈设置
- 基础资金管理

## 第二阶段：高级功能
### 完整马丁格尔策略
- 复杂的网格交易逻辑
- 智能加仓时机判断
- 多级止盈策略

### 增强风险管理
- 动态止损调整
- 资金百分比管理
- 最大回撤控制

### 用户界面优化
- 图表信息显示
- 状态指示器
- 参数配置界面

## 第三阶段：高级特性
### 智能分析功能
- 市场趋势分析
- 最佳入场时机判断
- 动态参数调整建议

### 性能优化
- 代码执行效率优化
- 内存使用优化
- 网络通信优化

### 扩展功能
- 多货币对支持
- 策略组合功能
- 高级统计分析

# 逻辑依赖链 (Logical Dependency Chain)
## 基础依赖顺序
1. **EA框架搭建** → 所有功能的基础
2. **参数系统** → 为交易逻辑提供配置
3. **订单管理** → 交易执行的核心
4. **基础马丁格尔逻辑** → 策略实现的核心
5. **风险控制** → 保证交易安全
6. **日志系统** → 监控和调试支持
7. **用户界面** → 提升用户体验
8. **高级功能** → 增强策略效果

## 快速可用性路径
- 优先实现基础的开仓和平仓功能
- 快速搭建可视化的交易状态显示
- 确保每个阶段都有可测试的功能模块

## 原子化功能设计
- 每个功能模块独立可测试
- 模块间松耦合设计
- 支持渐进式功能增强

# 风险和缓解措施 (Risks and Mitigations)
## 技术挑战
### 风险：订单执行延迟和滑点
- 缓解：实现重试机制和滑点容忍度设置
- 监控：记录执行时间和滑点统计

### 风险：网络连接中断
- 缓解：实现断线重连和状态恢复机制
- 监控：连接状态检查和自动重连

### 风险：内存泄漏和性能问题
- 缓解：严格的内存管理和代码优化
- 监控：性能指标跟踪和资源使用监控

## MVP确定挑战
### 风险：功能范围过大
- 缓解：专注核心马丁格尔逻辑，延后高级功能
- 策略：先实现基础交易，再添加智能分析

### 风险：用户体验复杂
- 缓解：简化初始参数设置，提供默认配置
- 策略：渐进式功能暴露，避免功能过载

## 资源约束
### 风险：开发时间限制
- 缓解：采用模块化开发，优先核心功能
- 策略：建立清晰的里程碑和交付计划

### 风险：测试环境限制
- 缓解：使用模拟账户进行充分测试
- 策略：建立自动化测试流程

# 附录 (Appendix)
## 技术规范
### MQL4编码标准
- 遵循MQL4官方编码规范
- 使用匈牙利命名法
- 详细的代码注释（中英文对照）
- 错误处理和异常管理

### 性能要求
- 订单执行时间 < 100ms
- 内存使用 < 50MB
- CPU使用率 < 5%
- 支持24/7连续运行

### 兼容性要求
- MetaTrader 4 Build 1090+
- Windows 7/8/10/11
- 主流外汇经纪商平台
- 标准和ECN账户类型

## 测试和验证标准
### 功能测试
- 所有交易参数的边界值测试
- 异常市场条件下的稳定性测试
- 长期运行的压力测试

### 性能测试
- 高频交易场景测试
- 网络延迟和断线恢复测试
- 内存和CPU使用率监控

### 安全测试
- 资金安全和风险控制验证
- 异常情况下的保护机制测试
- 数据完整性和一致性检查
</PRD>
