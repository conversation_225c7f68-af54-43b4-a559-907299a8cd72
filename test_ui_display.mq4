//+------------------------------------------------------------------+
//|                                            test_ui_display.mq4 |
//|                          Test script for UI and display system |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test UI Display"
#property version   "1.00"
#property strict
#property script_show_inputs

// 测试UI参数
extern bool TestShowInfoPanel = true;
extern bool TestShowGridLines = true;
extern bool TestShowStatusInfo = true;
extern int TestInfoPanelCorner = 0;
extern color TestInfoTextColor = clrWhite;
extern color TestProfitColor = clrLime;
extern color TestLossColor = clrRed;

// UI对象名称前缀
#define TEST_UI_PREFIX "TestMartingale_"

//+------------------------------------------------------------------+
//| 创建测试文本标签                                                    |
//+------------------------------------------------------------------+
void CreateTestTextLabel(string name, string text, int x, int y, color textColor)
{
   ObjectDelete(0, name);
   
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_CORNER, TestInfoPanelCorner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 创建测试信息面板                                                    |
//+------------------------------------------------------------------+
void CreateTestInfoPanel()
{
   if(!TestShowInfoPanel)
   {
      return;
   }
   
   int x = 10, y = 20;
   if(TestInfoPanelCorner == 1 || TestInfoPanelCorner == 3) x = 200;
   if(TestInfoPanelCorner == 2 || TestInfoPanelCorner == 3) y = 100;
   
   // 创建背景
   string panelName = TEST_UI_PREFIX + "InfoBackground";
   ObjectDelete(0, panelName);
   
   ObjectCreate(0, panelName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, panelName, OBJPROP_CORNER, TestInfoPanelCorner);
   ObjectSetInteger(0, panelName, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, panelName, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, panelName, OBJPROP_XSIZE, 250);
   ObjectSetInteger(0, panelName, OBJPROP_YSIZE, 300);
   ObjectSetInteger(0, panelName, OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(0, panelName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, panelName, OBJPROP_COLOR, clrGray);
   ObjectSetInteger(0, panelName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, panelName, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, panelName, OBJPROP_BACK, false);
   ObjectSetInteger(0, panelName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, panelName, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, panelName, OBJPROP_HIDDEN, true);
   
   // 创建信息文本
   string infoText = "";
   infoText += "=== Martingale EA 测试 ===\n";
   infoText += "状态: 测试模式\n";
   infoText += "时间: " + TimeToString(TimeCurrent(), TIME_SECONDS) + "\n";
   infoText += "货币对: " + Symbol() + "\n\n";
   
   infoText += "=== 持仓信息 ===\n";
   infoText += "订单数量: 3/7\n";
   infoText += "网格级别: 2\n";
   infoText += "浮动盈亏: +15.50\n";
   infoText += "总盈亏: +125.75\n\n";
   
   infoText += "=== 账户信息 ===\n";
   infoText += "余额: " + DoubleToString(AccountBalance(), 2) + "\n";
   infoText += "净值: " + DoubleToString(AccountEquity(), 2) + "\n";
   infoText += "可用保证金: " + DoubleToString(AccountFreeMargin(), 2) + "\n\n";
   
   infoText += "=== 策略参数 ===\n";
   infoText += "初始手数: 0.01\n";
   infoText += "加仓倍数: 2.0\n";
   infoText += "网格间距: 30 点\n";
   infoText += "交易方向: 双向\n";
   infoText += "市场波动率: 0.125%\n";
   infoText += "动态间距: 35.2 点\n";
   
   CreateTestTextLabel(TEST_UI_PREFIX + "InfoPanel", infoText, x + 5, y + 5, TestInfoTextColor);
}

//+------------------------------------------------------------------+
//| 创建测试网格线                                                      |
//+------------------------------------------------------------------+
void CreateTestGridLines()
{
   if(!TestShowGridLines)
   {
      return;
   }
   
   // 模拟网格价格
   double currentPrice = (Bid + Ask) / 2;
   double gridPrices[] = {
      currentPrice + 30 * Point,
      currentPrice,
      currentPrice - 30 * Point,
      currentPrice - 60 * Point
   };
   
   // 绘制网格线
   for(int i = 0; i < ArraySize(gridPrices); i++)
   {
      string lineName = TEST_UI_PREFIX + "GridLine_" + IntegerToString(i);
      
      ObjectDelete(0, lineName);
      ObjectCreate(0, lineName, OBJ_HLINE, 0, 0, gridPrices[i]);
      ObjectSetInteger(0, lineName, OBJPROP_COLOR, clrYellow);
      ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_DOT);
      ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
      ObjectSetString(0, lineName, OBJPROP_TEXT, "Grid L" + IntegerToString(i));
      ObjectSetInteger(0, lineName, OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, lineName, OBJPROP_HIDDEN, true);
   }
   
   // 绘制MA线
   double ma20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, 0);
   if(ma20 > 0)
   {
      string maLineName = TEST_UI_PREFIX + "MA20_Line";
      ObjectDelete(0, maLineName);
      ObjectCreate(0, maLineName, OBJ_HLINE, 0, 0, ma20);
      ObjectSetInteger(0, maLineName, OBJPROP_COLOR, clrBlue);
      ObjectSetInteger(0, maLineName, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, maLineName, OBJPROP_WIDTH, 2);
      ObjectSetString(0, maLineName, OBJPROP_TEXT, "MA20: " + DoubleToString(ma20, Digits));
      ObjectSetInteger(0, maLineName, OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, maLineName, OBJPROP_HIDDEN, true);
   }
}

//+------------------------------------------------------------------+
//| 创建测试状态栏                                                      |
//+------------------------------------------------------------------+
void CreateTestStatusBar()
{
   if(!TestShowStatusInfo)
   {
      return;
   }
   
   string statusText = "Martingale EA | 测试模式 | 订单:3 | 盈亏:+15.5";
   
   CreateTestTextLabel(TEST_UI_PREFIX + "StatusBar", statusText, 10, 5, TestInfoTextColor);
}

//+------------------------------------------------------------------+
//| 清理测试UI对象                                                      |
//+------------------------------------------------------------------+
void CleanupTestUI()
{
   for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, TEST_UI_PREFIX) == 0)
      {
         ObjectDelete(0, objName);
      }
   }
   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 测试颜色和字体设置                                                  |
//+------------------------------------------------------------------+
void TestColorAndFont()
{
   // 测试不同颜色的文本
   CreateTestTextLabel(TEST_UI_PREFIX + "ColorTest1", "盈利文本测试", 300, 50, TestProfitColor);
   CreateTestTextLabel(TEST_UI_PREFIX + "ColorTest2", "亏损文本测试", 300, 70, TestLossColor);
   CreateTestTextLabel(TEST_UI_PREFIX + "ColorTest3", "普通文本测试", 300, 90, TestInfoTextColor);
   
   // 测试不同字体大小
   string fontTestName = TEST_UI_PREFIX + "FontTest";
   ObjectDelete(0, fontTestName);
   ObjectCreate(0, fontTestName, OBJ_LABEL, 0, 0, 0);
   ObjectSetString(0, fontTestName, OBJPROP_TEXT, "字体测试 - 大号字体");
   ObjectSetInteger(0, fontTestName, OBJPROP_CORNER, 0);
   ObjectSetInteger(0, fontTestName, OBJPROP_XDISTANCE, 300);
   ObjectSetInteger(0, fontTestName, OBJPROP_YDISTANCE, 110);
   ObjectSetInteger(0, fontTestName, OBJPROP_COLOR, clrCyan);
   ObjectSetString(0, fontTestName, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(0, fontTestName, OBJPROP_FONTSIZE, 12);
   ObjectSetInteger(0, fontTestName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, fontTestName, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== 开始测试 Martingale EA 用户界面系统 ===");
   
   // 清理现有UI对象
   CleanupTestUI();
   
   // 测试1: 信息面板显示
   Print("测试1: 信息面板显示");
   CreateTestInfoPanel();
   Print("信息面板创建完成，位置: ", TestInfoPanelCorner == 0 ? "左上" : 
         TestInfoPanelCorner == 1 ? "右上" : 
         TestInfoPanelCorner == 2 ? "左下" : "右下");
   
   // 测试2: 网格线绘制
   Print("测试2: 网格线绘制");
   CreateTestGridLines();
   Print("网格线绘制完成");
   
   // 测试3: 状态栏显示
   Print("测试3: 状态栏显示");
   CreateTestStatusBar();
   Print("状态栏创建完成");
   
   // 测试4: 颜色和字体
   Print("测试4: 颜色和字体测试");
   TestColorAndFont();
   Print("颜色和字体测试完成");
   
   // 刷新图表
   ChartRedraw(0);
   
   // 测试5: UI对象统计
   Print("测试5: UI对象统计");
   int uiObjectCount = 0;
   for(int i = 0; i < ObjectsTotal(0); i++)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, TEST_UI_PREFIX) == 0)
      {
         uiObjectCount++;
         Print("UI对象: ", objName);
      }
   }
   Print("总共创建了 ", uiObjectCount, " 个UI对象");
   
   // 测试6: 性能测试
   Print("测试6: UI更新性能测试");
   datetime startTime = TimeCurrent();
   
   for(int j = 0; j < 10; j++)
   {
      CreateTestInfoPanel();
      CreateTestGridLines();
      CreateTestStatusBar();
   }
   
   datetime endTime = TimeCurrent();
   Print("10次UI更新耗时: ", (endTime - startTime), " 秒");
   
   Print("=== UI系统测试完成 ===");
   Print("结论: UI系统功能正常，支持多种显示元素和自定义配置");
   Print("注意: 测试UI对象将在5秒后自动清理");
   
   // 5秒后清理测试对象
   Sleep(5000);
   CleanupTestUI();
   Print("测试UI对象已清理");
}
//+------------------------------------------------------------------+
