//+------------------------------------------------------------------+
//|                                             test_logging.mq4 |
//|                              Test script for logging system |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test Logging"
#property version   "1.00"
#property strict
#property script_show_inputs

// 测试参数
extern bool TestFileLogging = true;
extern bool TestDebugMode = true;
extern int TestLogLevel = 2;

// 日志级别常量
#define LOG_LEVEL_ERROR   0
#define LOG_LEVEL_INFO    1
#define LOG_LEVEL_DEBUG   2

// 测试日志变量
string g_TestLogFileName = "";
int g_TestLogFileHandle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| 测试日志初始化                                                      |
//+------------------------------------------------------------------+
bool TestInitializeLogging()
{
   if(!TestFileLogging)
   {
      return true;
   }
   
   datetime currentTime = TimeCurrent();
   string dateStr = TimeToString(currentTime, TIME_DATE);
   StringReplace(dateStr, ".", "_");
   
   g_TestLogFileName = "TestLogging_" + dateStr + ".log";
   
   g_TestLogFileHandle = FileOpen(g_TestLogFileName, FILE_WRITE | FILE_TXT | FILE_SHARE_READ);
   
   if(g_TestLogFileHandle == INVALID_HANDLE)
   {
      Print("错误: 无法创建测试日志文件 ", g_TestLogFileName);
      return false;
   }
   
   string header = "=== 日志系统测试开始 ===\n";
   header += "时间: " + TimeToString(currentTime) + "\n";
   header += "========================\n";
   
   FileWrite(g_TestLogFileHandle, header);
   FileFlush(g_TestLogFileHandle);
   
   return true;
}

//+------------------------------------------------------------------+
//| 测试日志函数                                                        |
//+------------------------------------------------------------------+
void TestLogInfo(string message)
{
   if(TestLogLevel < LOG_LEVEL_INFO)
   {
      return;
   }
   
   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string logMessage = "[" + timestamp + "] [INFO] " + message;
   
   Print(logMessage);
   
   if(TestFileLogging && g_TestLogFileHandle != INVALID_HANDLE)
   {
      FileWrite(g_TestLogFileHandle, logMessage);
      FileFlush(g_TestLogFileHandle);
   }
}

void TestLogError(string message, int errorCode = 0)
{
   if(TestLogLevel < LOG_LEVEL_ERROR)
   {
      return;
   }
   
   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string logMessage = "[" + timestamp + "] [ERROR] " + message;
   
   if(errorCode != 0)
   {
      logMessage += " (错误代码: " + IntegerToString(errorCode) + ")";
   }
   
   Print(logMessage);
   
   if(TestFileLogging && g_TestLogFileHandle != INVALID_HANDLE)
   {
      FileWrite(g_TestLogFileHandle, logMessage);
      FileFlush(g_TestLogFileHandle);
   }
}

void TestLogDebug(string message)
{
   if(TestLogLevel < LOG_LEVEL_DEBUG || !TestDebugMode)
   {
      return;
   }
   
   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string logMessage = "[" + timestamp + "] [DEBUG] " + message;
   
   Print(logMessage);
   
   if(TestFileLogging && g_TestLogFileHandle != INVALID_HANDLE)
   {
      FileWrite(g_TestLogFileHandle, logMessage);
      FileFlush(g_TestLogFileHandle);
   }
}

//+------------------------------------------------------------------+
//| 测试错误分类                                                        |
//+------------------------------------------------------------------+
string TestClassifyError(int errorCode)
{
   if(errorCode == ERR_NO_ERROR)
      return "正常";
   else if(errorCode >= 4000 && errorCode < 5000)
      return "运行时错误";
   else if(errorCode >= 1 && errorCode < 100)
      return "交易错误";
   else if(errorCode >= 130 && errorCode < 150)
      return "价格错误";
   else
      return "未知错误类型";
}

//+------------------------------------------------------------------+
//| 关闭测试日志                                                        |
//+------------------------------------------------------------------+
void TestCloseLogging()
{
   if(g_TestLogFileHandle != INVALID_HANDLE)
   {
      string footer = "\n=== 日志系统测试结束 ===\n";
      footer += "时间: " + TimeToString(TimeCurrent()) + "\n";
      footer += "========================\n";
      
      FileWrite(g_TestLogFileHandle, footer);
      FileClose(g_TestLogFileHandle);
      g_TestLogFileHandle = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== 开始测试 Martingale EA 日志系统 ===");
   
   // 测试1: 日志系统初始化
   Print("测试1: 日志系统初始化");
   bool initResult = TestInitializeLogging();
   Print("初始化结果: ", initResult ? "成功" : "失败");
   
   if(!initResult && TestFileLogging)
   {
      Print("文件日志初始化失败，仅测试控制台日志");
   }
   
   // 测试2: 不同级别的日志记录
   Print("测试2: 不同级别的日志记录");
   
   TestLogInfo("这是一条信息日志");
   TestLogError("这是一条错误日志");
   TestLogDebug("这是一条调试日志");
   
   // 测试3: 带错误代码的日志
   Print("测试3: 带错误代码的日志");
   
   TestLogError("模拟交易错误", ERR_INVALID_TRADE_PARAMETERS);
   TestLogError("模拟服务器错误", ERR_SERVER_BUSY);
   TestLogError("模拟连接错误", ERR_NO_CONNECTION);
   
   // 测试4: 错误分类测试
   Print("测试4: 错误分类测试");
   
   int[] testErrors = {ERR_NO_ERROR, ERR_INVALID_TRADE_PARAMETERS, ERR_SERVER_BUSY, 
                       ERR_NO_CONNECTION, ERR_PRICE_CHANGED, 4051, 4108};
   
   for(int i = 0; i < ArraySize(testErrors); i++)
   {
      string errorType = TestClassifyError(testErrors[i]);
      TestLogInfo("错误代码 " + IntegerToString(testErrors[i]) + " 分类: " + errorType);
   }
   
   // 测试5: 大量日志写入测试
   Print("测试5: 大量日志写入测试");
   
   datetime startTime = TimeCurrent();
   
   for(int j = 0; j < 100; j++)
   {
      TestLogDebug("批量日志测试 #" + IntegerToString(j+1));
      
      if(j % 20 == 0)
      {
         TestLogInfo("批量测试进度: " + IntegerToString(j+1) + "/100");
      }
   }
   
   datetime endTime = TimeCurrent();
   int duration = (int)(endTime - startTime);
   
   TestLogInfo("批量日志测试完成，耗时: " + IntegerToString(duration) + " 秒");
   
   // 测试6: 日志级别过滤测试
   Print("测试6: 日志级别过滤测试");
   
   int originalLogLevel = TestLogLevel;
   
   // 设置为只记录错误
   TestLogLevel = LOG_LEVEL_ERROR;
   TestLogInfo("这条信息日志应该被过滤");
   TestLogError("这条错误日志应该显示");
   
   // 恢复原始级别
   TestLogLevel = originalLogLevel;
   TestLogInfo("日志级别已恢复");
   
   // 测试7: 文件日志验证
   Print("测试7: 文件日志验证");
   
   if(TestFileLogging && g_TestLogFileHandle != INVALID_HANDLE)
   {
      TestLogInfo("文件日志功能正常工作");
      Print("日志文件: ", g_TestLogFileName);
   }
   else
   {
      Print("文件日志未启用或初始化失败");
   }
   
   // 测试8: 交易日志模拟
   Print("测试8: 交易日志模拟");
   
   TestLogInfo("交易操作: 开仓 | Ticket=12345 | Type=BUY | Lots=0.01 | Price=1.2345 | 结果=成功");
   TestLogInfo("交易操作: 平仓 | Ticket=12345 | Type=BUY | Lots=0.01 | Price=1.2350 | 结果=成功");
   
   // 清理和总结
   TestLogInfo("=== 日志系统测试完成 ===");
   TestCloseLogging();
   
   Print("=== Martingale EA 日志系统测试完成 ===");
   Print("结论: 日志系统功能正常，支持多级别日志和文件输出");
}
//+------------------------------------------------------------------+
