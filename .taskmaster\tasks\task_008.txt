# Task ID: 8
# Title: 用户界面和信息显示 (User Interface and Information Display)
# Status: pending
# Dependencies: None
# Priority: medium
# Description: 实现图表上的信息显示，包括当前持仓状态、盈亏情况和策略参数显示
# Details:
1. 创建DisplayInfo()函数，在图表上显示当前交易信息
2. 创建DrawLines()函数，绘制重要的价格线和支撑阻力位
3. 创建ShowStatus()函数，显示EA的运行状态和参数
4. 显示当前持仓状态，包括订单数量和总盈亏
5. 显示策略参数，如当前手数、加仓级别等
6. 实现可配置的界面元素，允许用户自定义显示内容
7. 添加颜色和字体设置，提高界面的可读性

# Test Strategy:
用户体验测试，验证信息显示的准确性和可读性
