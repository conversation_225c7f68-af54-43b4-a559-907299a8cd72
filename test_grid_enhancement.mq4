//+------------------------------------------------------------------+
//|                                      test_grid_enhancement.mq4 |
//|                        Test script for enhanced grid trading |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test Grid Enhancement"
#property version   "1.00"
#property strict
#property script_show_inputs

// 测试参数
extern bool TestDynamicGrid = true;
extern double TestVolatilityMultiplier = 1.5;
extern int TestMinGridStep = 15;
extern int TestMaxGridStep = 100;
extern int TestBaseGridStep = 30;

//+------------------------------------------------------------------+
//| 测试波动率计算                                                      |
//+------------------------------------------------------------------+
double TestCalculateVolatility()
{
   // 使用ATR(14)计算波动率
   double atr = iATR(Symbol(), PERIOD_CURRENT, 14, 0);
   
   if(atr <= 0)
   {
      // 如果ATR无效，使用简单的价格范围计算
      double high = iHigh(Symbol(), PERIOD_CURRENT, 0);
      double low = iLow(Symbol(), PERIOD_CURRENT, 0);
      atr = high - low;
   }
   
   // 标准化波动率（相对于平均价格）
   double avgPrice = (Bid + Ask) / 2;
   double volatility = (atr / avgPrice) * 100; // 转换为百分比
   
   return volatility;
}

//+------------------------------------------------------------------+
//| 测试动态网格间距计算                                                |
//+------------------------------------------------------------------+
double TestDynamicGridStep(int level, double volatility)
{
   if(!TestDynamicGrid)
   {
      return TestBaseGridStep;
   }
   
   // 基础网格间距
   double baseStep = TestBaseGridStep;
   
   // 根据波动率调整间距
   double adjustedStep = baseStep * (1.0 + volatility * TestVolatilityMultiplier);
   
   // 根据级别调整间距
   double levelMultiplier = 1.0 + (level * 0.2); // 每级增加20%
   adjustedStep *= levelMultiplier;
   
   // 限制在最小和最大间距范围内
   if(adjustedStep < TestMinGridStep) adjustedStep = TestMinGridStep;
   if(adjustedStep > TestMaxGridStep) adjustedStep = TestMaxGridStep;
   
   return adjustedStep;
}

//+------------------------------------------------------------------+
//| 测试网格密度检查                                                    |
//+------------------------------------------------------------------+
bool TestCheckGridDensity(double prices[], int count)
{
   if(count < 2)
   {
      return true;
   }
   
   // 计算平均网格间距
   double totalDistance = 0;
   int validDistances = 0;
   
   for(int i = 1; i < count; i++)
   {
      double distance = MathAbs(prices[i] - prices[i-1]) / Point;
      if(distance > 0)
      {
         totalDistance += distance;
         validDistances++;
      }
   }
   
   if(validDistances == 0)
   {
      return true;
   }
   
   double avgDistance = totalDistance / validDistances;
   
   // 如果平均间距小于最小网格间距的80%，认为过于密集
   return (avgDistance >= TestMinGridStep * 0.8);
}

//+------------------------------------------------------------------+
//| 测试网格重置条件                                                    |
//+------------------------------------------------------------------+
bool TestShouldResetGrid(double totalProfit, int gridCount, double drawdown)
{
   double resetThreshold = 50.0; // 测试重置阈值
   
   // 检查总盈利
   if(totalProfit >= resetThreshold)
   {
      return true;
   }
   
   // 检查网格复杂度
   int maxOrders = 7;
   if(gridCount >= maxOrders * 0.8)
   {
      return true;
   }
   
   // 检查回撤
   double balance = AccountBalance();
   if(drawdown > balance * 0.1)
   {
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== 开始测试 Martingale EA 网格交易增强 ===");
   
   // 测试1: 波动率计算测试
   Print("测试1: 波动率计算测试");
   
   double volatility = TestCalculateVolatility();
   Print("当前市场波动率: ", DoubleToString(volatility, 4), "%");
   
   if(volatility > 0.5)
   {
      Print("波动率评估: 高波动率市场");
   }
   else if(volatility < 0.1)
   {
      Print("波动率评估: 低波动率市场");
   }
   else
   {
      Print("波动率评估: 中等波动率市场");
   }
   
   // 测试2: 动态网格间距测试
   Print("测试2: 动态网格间距测试");
   
   Print("基础网格间距: ", TestBaseGridStep, " 点");
   Print("波动率乘数: ", TestVolatilityMultiplier);
   Print("间距范围: ", TestMinGridStep, " - ", TestMaxGridStep, " 点");
   
   for(int level = 0; level < 5; level++)
   {
      double dynamicStep = TestDynamicGridStep(level, volatility);
      Print("级别 ", level, " 动态间距: ", DoubleToString(dynamicStep, 1), " 点");
   }
   
   // 测试3: 网格密度控制测试
   Print("测试3: 网格密度控制测试");
   
   // 模拟不同密度的网格价格
   double normalPrices[] = {1.2000, 1.2030, 1.2060, 1.2090}; // 正常间距
   double densePrices[] = {1.2000, 1.2010, 1.2015, 1.2020};  // 密集间距
   
   bool normalDensity = TestCheckGridDensity(normalPrices, ArraySize(normalPrices));
   bool denseDensity = TestCheckGridDensity(densePrices, ArraySize(densePrices));
   
   Print("正常间距网格密度检查: ", normalDensity ? "通过" : "过密");
   Print("密集间距网格密度检查: ", denseDensity ? "通过" : "过密");
   
   // 测试4: 网格重置条件测试
   Print("测试4: 网格重置条件测试");
   
   // 模拟不同场景
   struct TestScenario
   {
      string name;
      double profit;
      int gridCount;
      double drawdown;
   };
   
   TestScenario scenarios[] = 
   {
      {"正常交易", 20.0, 3, 5.0},
      {"达到盈利阈值", 60.0, 4, 8.0},
      {"网格过多", 30.0, 6, 12.0},
      {"回撤过大", 25.0, 4, 150.0}
   };
   
   for(int i = 0; i < ArraySize(scenarios); i++)
   {
      bool shouldReset = TestShouldResetGrid(scenarios[i].profit, scenarios[i].gridCount, scenarios[i].drawdown);
      Print("场景 '", scenarios[i].name, "': ", shouldReset ? "需要重置" : "继续交易");
   }
   
   // 测试5: 不等间距网格测试
   Print("测试5: 不等间距网格测试");
   
   Print("不等间距网格示例:");
   double basePrice = Ask;
   
   for(int j = 0; j < 5; j++)
   {
      double stepSize = TestDynamicGridStep(j, volatility);
      double gridPrice = basePrice - (j + 1) * stepSize * Point;
      
      Print("网格 ", j+1, ": 价格=", DoubleToString(gridPrice, Digits), 
           " 间距=", DoubleToString(stepSize, 1), " 点");
   }
   
   // 测试6: 市场趋势分析测试
   Print("测试6: 市场趋势分析测试");
   
   double ma20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, 0);
   double currentPrice = (Bid + Ask) / 2;
   
   Print("当前价格: ", DoubleToString(currentPrice, Digits));
   Print("MA20: ", DoubleToString(ma20, Digits));
   
   if(currentPrice > ma20)
   {
      Print("趋势分析: 上升趋势，建议买入网格");
   }
   else if(currentPrice < ma20)
   {
      Print("趋势分析: 下降趋势，建议卖出网格");
   }
   else
   {
      Print("趋势分析: 横盘整理，适合双向网格");
   }
   
   // 测试7: 网格优化建议
   Print("测试7: 网格优化建议");
   
   if(volatility > 0.5)
   {
      Print("优化建议: 高波动率环境");
      Print("- 增加网格间距以减少频繁交易");
      Print("- 考虑降低最大订单数");
      Print("- 加强风险控制");
   }
   else if(volatility < 0.1)
   {
      Print("优化建议: 低波动率环境");
      Print("- 可以减少网格间距以增加交易频率");
      Print("- 考虑增加网格密度");
      Print("- 适合精细化网格策略");
   }
   else
   {
      Print("优化建议: 中等波动率环境");
      Print("- 使用标准网格设置");
      Print("- 平衡风险和收益");
      Print("- 监控市场变化调整策略");
   }
   
   Print("=== 网格交易增强测试完成 ===");
   Print("结论: 增强网格系统功能完善，能够适应不同市场条件");
}
//+------------------------------------------------------------------+
