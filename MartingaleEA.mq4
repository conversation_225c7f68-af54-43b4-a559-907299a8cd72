//+------------------------------------------------------------------+
//|                                                  MartingaleEA.mq4 |
//|                                    Copyright 2024, Martingale EA |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Martingale EA"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 外部参数定义 (External Parameters Definition)                     |
//+------------------------------------------------------------------+
// 1. 初始手数参数
extern double Lots = 0.01;              // 初始交易手数

// 2. 加仓倍数参数
extern double Multiplier = 2.0;         // 加仓手数倍数

// 3. 最大订单数参数
extern int MaxOrders = 7;                // 最大同时持有订单数

// 4. 加仓间距参数
extern int GridStep = 30;                // 网格加仓间距(点数)

// 5. 止盈止损参数
extern int TakeProfit = 50;              // 止盈点数
extern int StopLoss = 200;               // 止损点数

// 6. 交易方向参数
extern int TradeDirection = 2;           // 交易方向: 0=仅买入, 1=仅卖出, 2=双向

// 7. 魔术号码参数
extern int MagicNumber = 123456;         // EA识别订单的魔术号码

//+------------------------------------------------------------------+
//| 全局变量声明区域 (Global Variables Declaration)                    |
//+------------------------------------------------------------------+
// EA运行状态变量
bool g_IsInitialized = false;
bool g_IsTradeAllowed = true;
datetime g_LastTickTime = 0;

// 交易统计变量
int g_TotalOrders = 0;
double g_TotalProfit = 0.0;
int g_CurrentGridLevel = 0;

// 错误处理变量
int g_LastError = 0;
string g_LastErrorMessage = "";

//+------------------------------------------------------------------+
//| 参数验证函数 (Parameter Validation Function)                      |
//+------------------------------------------------------------------+
bool ValidateParameters()
{
   // 8. 添加参数范围验证逻辑，确保参数值在合理范围内
   Print("=== 开始参数验证 ===");

   bool isValid = true;

   // 验证初始手数
   if(Lots < 0.01 || Lots > 100.0)
   {
      Print("错误: 初始手数超出范围 (0.01-100.0), 当前值: ", Lots);
      isValid = false;
   }

   // 验证加仓倍数
   if(Multiplier < 1.1 || Multiplier > 10.0)
   {
      Print("错误: 加仓倍数超出范围 (1.1-10.0), 当前值: ", Multiplier);
      isValid = false;
   }

   // 验证最大订单数
   if(MaxOrders < 1 || MaxOrders > 20)
   {
      Print("错误: 最大订单数超出范围 (1-20), 当前值: ", MaxOrders);
      isValid = false;
   }

   // 验证网格间距
   if(GridStep < 5 || GridStep > 1000)
   {
      Print("错误: 网格间距超出范围 (5-1000点), 当前值: ", GridStep);
      isValid = false;
   }

   // 验证止盈点数
   if(TakeProfit < 5 || TakeProfit > 1000)
   {
      Print("错误: 止盈点数超出范围 (5-1000点), 当前值: ", TakeProfit);
      isValid = false;
   }

   // 验证止损点数
   if(StopLoss < 10 || StopLoss > 2000)
   {
      Print("错误: 止损点数超出范围 (10-2000点), 当前值: ", StopLoss);
      isValid = false;
   }

   // 验证交易方向
   if(TradeDirection < 0 || TradeDirection > 2)
   {
      Print("错误: 交易方向参数无效 (0-2), 当前值: ", TradeDirection);
      isValid = false;
   }

   // 验证魔术号码
   if(MagicNumber < 1 || MagicNumber > 999999999)
   {
      Print("错误: 魔术号码超出范围 (1-999999999), 当前值: ", MagicNumber);
      isValid = false;
   }

   // 逻辑验证：止盈应该小于止损
   if(TakeProfit >= StopLoss)
   {
      Print("警告: 止盈点数应该小于止损点数，当前 TP=", TakeProfit, " SL=", StopLoss);
   }

   // 显示验证结果
   if(isValid)
   {
      Print("=== 参数验证通过 ===");
      Print("配置参数:");
      Print("- 初始手数: ", Lots);
      Print("- 加仓倍数: ", Multiplier);
      Print("- 最大订单数: ", MaxOrders);
      Print("- 网格间距: ", GridStep, " 点");
      Print("- 止盈: ", TakeProfit, " 点");
      Print("- 止损: ", StopLoss, " 点");
      Print("- 交易方向: ", TradeDirection, " (0=买入,1=卖出,2=双向)");
      Print("- 魔术号码: ", MagicNumber);
   }
   else
   {
      Print("=== 参数验证失败 ===");
   }

   return isValid;
}

//+------------------------------------------------------------------+
//| Expert initialization function (专家顾问初始化函数)                |
//+------------------------------------------------------------------+
int OnInit()
{
   // 1. 设置EA启动时的基本配置
   Print("=== Martingale EA 初始化开始 ===");

   // 首先验证外部参数
   if(!ValidateParameters())
   {
      Print("错误: 参数验证失败，EA无法启动");
      return(INIT_PARAMETERS_INCORRECT);
   }

   // 检查交易权限
   if(!IsTradeAllowed())
   {
      Print("错误: 交易未被允许，请检查EA设置");
      return(INIT_FAILED);
   }

   // 检查账户类型
   if(IsDemo())
   {
      Print("警告: 当前为模拟账户");
   }
   else
   {
      Print("注意: 当前为真实账户，请谨慎操作");
   }

   // 初始化全局变量
   g_IsInitialized = true;
   g_IsTradeAllowed = true;
   g_LastTickTime = TimeCurrent();
   g_TotalOrders = 0;
   g_TotalProfit = 0.0;
   g_CurrentGridLevel = 0;
   g_LastError = 0;
   g_LastErrorMessage = "";

   // 显示EA基本信息
   Print("EA名称: Martingale Expert Advisor");
   Print("版本: 1.00");
   Print("账户号码: ", AccountNumber());
   Print("账户余额: ", AccountBalance());
   Print("当前货币对: ", Symbol());
   Print("当前时间框架: ", Period());

   Print("=== Martingale EA 初始化完成 ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function (专家顾问反初始化函数)            |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 2. 处理EA卸载时的清理工作
   Print("=== Martingale EA 反初始化开始 ===");

   // 根据反初始化原因进行不同处理
   switch(reason)
   {
      case REASON_PROGRAM:
         Print("反初始化原因: 程序终止");
         break;
      case REASON_REMOVE:
         Print("反初始化原因: EA被移除");
         break;
      case REASON_RECOMPILE:
         Print("反初始化原因: EA重新编译");
         break;
      case REASON_CHARTCHANGE:
         Print("反初始化原因: 图表切换");
         break;
      case REASON_CHARTCLOSE:
         Print("反初始化原因: 图表关闭");
         break;
      case REASON_PARAMETERS:
         Print("反初始化原因: 参数修改");
         break;
      case REASON_ACCOUNT:
         Print("反初始化原因: 账户切换");
         break;
      default:
         Print("反初始化原因: 未知原因 (", reason, ")");
         break;
   }

   // 清理全局变量
   g_IsInitialized = false;
   g_IsTradeAllowed = false;

   // 显示最终统计信息
   Print("最终统计:");
   Print("- 总订单数: ", g_TotalOrders);
   Print("- 总盈亏: ", g_TotalProfit);
   Print("- 最大网格级别: ", g_CurrentGridLevel);

   // 清理图表对象（如果有的话）
   ObjectsDeleteAll(0, "Martingale_");

   Print("=== Martingale EA 反初始化完成 ===");
}

//+------------------------------------------------------------------+
//| Expert tick function (专家顾问主要事件处理函数)                    |
//+------------------------------------------------------------------+
void OnTick()
{
   // 3. 处理每个价格变动

   // 检查EA是否已正确初始化
   if(!g_IsInitialized)
   {
      Print("错误: EA未正确初始化");
      return;
   }

   // 检查是否允许交易
   if(!IsTradeAllowed() || !g_IsTradeAllowed)
   {
      return;
   }

   // 更新最后Tick时间
   g_LastTickTime = TimeCurrent();

   // 获取当前市场信息
   double currentBid = Bid;
   double currentAsk = Ask;
   double currentSpread = Ask - Bid;

   // 基本市场检查
   if(currentBid <= 0 || currentAsk <= 0)
   {
      Print("错误: 无效的市场价格 Bid=", currentBid, " Ask=", currentAsk);
      return;
   }

   // 检查点差是否过大
   double maxSpread = 50 * Point; // 最大允许点差50点
   if(currentSpread > maxSpread)
   {
      Print("警告: 点差过大 (", currentSpread/Point, " 点), 暂停交易");
      return;
   }

   // 执行马丁格尔交易策略
   ExecuteMartingaleStrategy();

   // 更新错误状态
   int currentError = GetLastError();
   if(currentError != 0 && currentError != g_LastError)
   {
      g_LastError = currentError;
      g_LastErrorMessage = "错误代码: " + IntegerToString(currentError);
      Print("检测到新错误: ", g_LastErrorMessage);
   }
}

//+------------------------------------------------------------------+
//| 订单管理系统 (Order Management System)                            |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 创建OpenOrder()函数，实现新订单的开仓操作                        |
//+------------------------------------------------------------------+
int OpenOrder(int orderType, double lots, double price, double stopLoss, double takeProfit, string comment = "")
{
   int ticket = -1;
   int retries = 3;

   // 标准化价格
   price = NormalizeDouble(price, Digits);
   if(stopLoss > 0) stopLoss = NormalizeDouble(stopLoss, Digits);
   if(takeProfit > 0) takeProfit = NormalizeDouble(takeProfit, Digits);

   // 验证手数
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

   if(lots < minLot || lots > maxLot)
   {
      Print("错误: 手数超出范围 Min=", minLot, " Max=", maxLot, " Current=", lots);
      return -1;
   }

   // 重试机制处理网络延迟
   for(int i = 0; i < retries; i++)
   {
      RefreshRates(); // 刷新价格

      ticket = OrderSend(Symbol(), orderType, lots, price, 3, stopLoss, takeProfit, comment, MagicNumber, 0, clrNONE);

      if(ticket > 0)
      {
         Print("订单开仓成功: Ticket=", ticket, " Type=", orderType, " Lots=", lots, " Price=", price);
         g_TotalOrders++;
         return ticket;
      }
      else
      {
         int error = GetLastError();
         Print("订单开仓失败 (尝试 ", i+1, "/", retries, "): 错误=", error, " ", ErrorDescription(error));

         // 处理特定错误
         if(error == ERR_TRADE_DISABLED || error == ERR_TRADE_NOT_ALLOWED)
         {
            Print("交易被禁用，停止重试");
            break;
         }

         Sleep(1000); // 等待1秒后重试
      }
   }

   return -1;
}

//+------------------------------------------------------------------+
//| 2. 创建CloseOrder()函数，实现订单的平仓操作                         |
//+------------------------------------------------------------------+
bool CloseOrder(int ticket, double lots = 0)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   // 检查订单是否属于当前EA
   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA Magic=", OrderMagicNumber(), " Expected=", MagicNumber);
      return false;
   }

   // 如果未指定手数，关闭全部
   if(lots <= 0) lots = OrderLots();

   double closePrice;
   color closeColor = clrRed;

   // 根据订单类型确定平仓价格
   if(OrderType() == OP_BUY)
   {
      closePrice = Bid;
      closeColor = clrBlue;
   }
   else if(OrderType() == OP_SELL)
   {
      closePrice = Ask;
      closeColor = clrRed;
   }
   else
   {
      Print("错误: 不支持的订单类型 ", OrderType());
      return false;
   }

   closePrice = NormalizeDouble(closePrice, Digits);

   // 重试机制
   for(int i = 0; i < 3; i++)
   {
      RefreshRates();

      if(OrderClose(ticket, lots, closePrice, 3, closeColor))
      {
         Print("订单平仓成功: Ticket=", ticket, " Lots=", lots, " Price=", closePrice);
         double profit = OrderProfit() + OrderSwap() + OrderCommission();
         g_TotalProfit += profit;
         return true;
      }
      else
      {
         int error = GetLastError();
         Print("订单平仓失败 (尝试 ", i+1, "/3): 错误=", error, " ", ErrorDescription(error));
         Sleep(1000);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 3. 创建ModifyOrder()函数，实现订单的修改操作                        |
//+------------------------------------------------------------------+
bool ModifyOrder(int ticket, double price, double stopLoss, double takeProfit)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   // 检查订单是否属于当前EA
   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA");
      return false;
   }

   // 标准化价格
   price = NormalizeDouble(price, Digits);
   if(stopLoss > 0) stopLoss = NormalizeDouble(stopLoss, Digits);
   if(takeProfit > 0) takeProfit = NormalizeDouble(takeProfit, Digits);

   // 检查是否需要修改
   if(price == OrderOpenPrice() && stopLoss == OrderStopLoss() && takeProfit == OrderTakeProfit())
   {
      Print("订单无需修改: Ticket=", ticket);
      return true;
   }

   // 重试机制
   for(int i = 0; i < 3; i++)
   {
      if(OrderModify(ticket, price, stopLoss, takeProfit, 0, clrYellow))
      {
         Print("订单修改成功: Ticket=", ticket, " SL=", stopLoss, " TP=", takeProfit);
         return true;
      }
      else
      {
         int error = GetLastError();
         Print("订单修改失败 (尝试 ", i+1, "/3): 错误=", error, " ", ErrorDescription(error));
         Sleep(1000);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 4. 创建GetOrderInfo()函数，实现订单信息的查询功能                   |
//+------------------------------------------------------------------+
bool GetOrderInfo(int ticket, double &openPrice, double &lots, int &orderType, double &profit, datetime &openTime)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   // 检查订单是否属于当前EA
   if(OrderMagicNumber() != MagicNumber)
   {
      return false;
   }

   // 获取订单信息
   openPrice = OrderOpenPrice();
   lots = OrderLots();
   orderType = OrderType();
   profit = OrderProfit() + OrderSwap() + OrderCommission();
   openTime = OrderOpenTime();

   return true;
}

//+------------------------------------------------------------------+
//| 5. 实现订单过滤机制，按魔术号码筛选EA相关订单                        |
//+------------------------------------------------------------------+
int CountOrdersByMagic(int magic = -1)
{
   if(magic == -1) magic = MagicNumber;

   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magic)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| 获取指定魔术号码的所有订单票号                                       |
//+------------------------------------------------------------------+
int GetOrderTicketsByMagic(int &tickets[], int magic = -1)
{
   if(magic == -1) magic = MagicNumber;

   ArrayResize(tickets, 0);

   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magic)
         {
            int size = ArraySize(tickets);
            ArrayResize(tickets, size + 1);
            tickets[size] = OrderTicket();
         }
      }
   }

   return ArraySize(tickets);
}

//+------------------------------------------------------------------+
//| 6. 实现魔术号码管理，确保订单标识的唯一性                            |
//+------------------------------------------------------------------+
bool IsMagicNumberUnique(int magic)
{
   // 检查是否有其他EA使用相同的魔术号码
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderMagicNumber() == magic && OrderSymbol() != Symbol())
         {
            return false; // 发现其他货币对使用相同魔术号码
         }
      }
   }
   return true;
}

//+------------------------------------------------------------------+
//| 7. 添加订单执行错误处理，处理网络延迟、滑点等异常情况                 |
//+------------------------------------------------------------------+
string ErrorDescription(int errorCode)
{
   switch(errorCode)
   {
      case ERR_NO_ERROR: return "无错误";
      case ERR_NO_RESULT: return "无结果";
      case ERR_COMMON_ERROR: return "一般错误";
      case ERR_INVALID_TRADE_PARAMETERS: return "无效的交易参数";
      case ERR_SERVER_BUSY: return "服务器忙";
      case ERR_OLD_VERSION: return "版本过旧";
      case ERR_NO_CONNECTION: return "无连接";
      case ERR_NOT_ENOUGH_RIGHTS: return "权限不足";
      case ERR_TOO_FREQUENT_REQUESTS: return "请求过于频繁";
      case ERR_MALFUNCTIONAL_TRADE: return "交易功能故障";
      case ERR_ACCOUNT_DISABLED: return "账户被禁用";
      case ERR_INVALID_ACCOUNT: return "无效账户";
      case ERR_TRADE_TIMEOUT: return "交易超时";
      case ERR_INVALID_PRICE: return "无效价格";
      case ERR_INVALID_STOPS: return "无效止损";
      case ERR_INVALID_TRADE_VOLUME: return "无效交易量";
      case ERR_MARKET_CLOSED: return "市场关闭";
      case ERR_TRADE_DISABLED: return "交易被禁用";
      case ERR_NOT_ENOUGH_MONEY: return "资金不足";
      case ERR_PRICE_CHANGED: return "价格改变";
      case ERR_OFF_QUOTES: return "无报价";
      case ERR_BROKER_BUSY: return "经纪商忙";
      case ERR_REQUOTE: return "重新报价";
      case ERR_ORDER_LOCKED: return "订单锁定";
      case ERR_LONG_POSITIONS_ONLY_ALLOWED: return "只允许多头";
      case ERR_TOO_MANY_REQUESTS: return "请求过多";
      default: return "未知错误 (" + IntegerToString(errorCode) + ")";
   }
}

//+------------------------------------------------------------------+
//| 关闭所有EA相关订单                                                  |
//+------------------------------------------------------------------+
bool CloseAllOrders()
{
   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);
   bool success = true;

   Print("开始关闭所有订单，共 ", count, " 个");

   for(int i = 0; i < count; i++)
   {
      if(!CloseOrder(tickets[i]))
      {
         success = false;
      }
   }

   return success;
}

//+------------------------------------------------------------------+
//| 基础马丁格尔逻辑 (Basic Martingale Logic)                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 实现CheckEntry()函数，判断首次开仓的市场条件                     |
//+------------------------------------------------------------------+
bool CheckEntry()
{
   // 检查是否已有订单
   if(CountOrdersByMagic() > 0)
   {
      return false; // 已有订单，不开新仓
   }

   // 检查交易时间
   if(!IsTradeTime())
   {
      return false;
   }

   // 检查账户资金
   double freeMargin = AccountFreeMargin();
   double requiredMargin = MarketInfo(Symbol(), MODE_MARGINREQUIRED) * Lots;

   if(freeMargin < requiredMargin * 2) // 保留2倍保证金
   {
      Print("警告: 可用保证金不足 Free=", freeMargin, " Required=", requiredMargin);
      return false;
   }

   // 检查点差
   double spread = (Ask - Bid) / Point;
   if(spread > 50) // 点差超过50点
   {
      Print("警告: 点差过大 ", spread, " 点");
      return false;
   }

   // 简单的入场条件（可以根据需要添加更复杂的逻辑）
   // 这里使用基本的价格波动作为入场信号
   static double lastPrice = 0;
   double currentPrice = (Bid + Ask) / 2;

   if(lastPrice == 0)
   {
      lastPrice = currentPrice;
      return false;
   }

   double priceChange = MathAbs(currentPrice - lastPrice);
   lastPrice = currentPrice;

   // 如果价格变动超过网格间距，可以考虑入场
   if(priceChange >= GridStep * Point)
   {
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 2. 实现CalculateLotSize()函数，根据加仓倍数计算新订单手数           |
//+------------------------------------------------------------------+
double CalculateLotSize(int level)
{
   double lotSize = Lots;

   // 根据加仓级别计算手数
   for(int i = 0; i < level; i++)
   {
      lotSize *= Multiplier;
   }

   // 检查手数限制
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

   // 标准化手数
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   // 限制在允许范围内
   if(lotSize < minLot) lotSize = minLot;
   if(lotSize > maxLot) lotSize = maxLot;

   Print("计算手数: Level=", level, " LotSize=", lotSize);

   return lotSize;
}

//+------------------------------------------------------------------+
//| 3. 实现CheckGrid()函数，判断网格加仓的时机和条件                    |
//+------------------------------------------------------------------+
bool CheckGrid(int &gridLevel, int &orderType, double &entryPrice)
{
   int tickets[];
   int orderCount = GetOrderTicketsByMagic(tickets);

   if(orderCount == 0)
   {
      return false; // 没有订单，不需要加仓
   }

   if(orderCount >= MaxOrders)
   {
      Print("已达到最大订单数限制: ", MaxOrders);
      return false;
   }

   // 找到最后一个订单
   int lastTicket = -1;
   datetime lastTime = 0;

   for(int i = 0; i < orderCount; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         if(OrderOpenTime() > lastTime)
         {
            lastTime = OrderOpenTime();
            lastTicket = OrderTicket();
         }
      }
   }

   if(lastTicket == -1)
   {
      return false;
   }

   // 获取最后订单信息
   if(!OrderSelect(lastTicket, SELECT_BY_TICKET))
   {
      return false;
   }

   double lastPrice = OrderOpenPrice();
   int lastType = OrderType();
   double currentPrice = (lastType == OP_BUY) ? Bid : Ask;

   // 计算价格距离
   double distance = MathAbs(currentPrice - lastPrice) / Point;

   // 检查是否达到加仓条件
   if(distance >= GridStep)
   {
      gridLevel = orderCount; // 当前级别

      // 确定加仓方向
      if(lastType == OP_BUY && currentPrice < lastPrice)
      {
         // 买单亏损，继续买入
         orderType = OP_BUY;
         entryPrice = Ask;
         return true;
      }
      else if(lastType == OP_SELL && currentPrice > lastPrice)
      {
         // 卖单亏损，继续卖出
         orderType = OP_SELL;
         entryPrice = Bid;
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 7. 实现加仓级别跟踪，记录当前加仓次数和级别                          |
//+------------------------------------------------------------------+
void UpdateGridLevel()
{
   int orderCount = CountOrdersByMagic();
   g_CurrentGridLevel = orderCount;

   if(orderCount > 0)
   {
      Print("当前网格级别: ", g_CurrentGridLevel, "/", MaxOrders);
   }
}

//+------------------------------------------------------------------+
//| 4. 支持买入模式(TradeDirection=0)的马丁格尔策略                     |
//+------------------------------------------------------------------+
bool ExecuteBuyStrategy()
{
   if(TradeDirection != 0 && TradeDirection != 2)
   {
      return false; // 不允许买入
   }

   // 检查首次开仓条件
   if(CountOrdersByMagic() == 0)
   {
      if(CheckEntry())
      {
         double lotSize = CalculateLotSize(0);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消买入操作");
            return false;
         }

         int ticket = OpenOrder(OP_BUY, lotSize, Ask, 0, 0, "Martingale Buy L0");
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }
   else
   {
      // 检查加仓条件
      int gridLevel, orderType;
      double entryPrice;

      if(CheckGrid(gridLevel, orderType, entryPrice) && orderType == OP_BUY)
      {
         double lotSize = CalculateLotSize(gridLevel);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消加仓买入操作");
            return false;
         }

         string comment = "Martingale Buy L" + IntegerToString(gridLevel);
         int ticket = OpenOrder(OP_BUY, lotSize, entryPrice, 0, 0, comment);
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 5. 支持卖出模式(TradeDirection=1)的马丁格尔策略                     |
//+------------------------------------------------------------------+
bool ExecuteSellStrategy()
{
   if(TradeDirection != 1 && TradeDirection != 2)
   {
      return false; // 不允许卖出
   }

   // 检查首次开仓条件
   if(CountOrdersByMagic() == 0)
   {
      if(CheckEntry())
      {
         double lotSize = CalculateLotSize(0);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消卖出操作");
            return false;
         }

         int ticket = OpenOrder(OP_SELL, lotSize, Bid, 0, 0, "Martingale Sell L0");
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }
   else
   {
      // 检查加仓条件
      int gridLevel, orderType;
      double entryPrice;

      if(CheckGrid(gridLevel, orderType, entryPrice) && orderType == OP_SELL)
      {
         double lotSize = CalculateLotSize(gridLevel);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消加仓卖出操作");
            return false;
         }

         string comment = "Martingale Sell L" + IntegerToString(gridLevel);
         int ticket = OpenOrder(OP_SELL, lotSize, entryPrice, 0, 0, comment);
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 6. 支持双向交易模式(TradeDirection=2)，同时处理买入和卖出           |
//+------------------------------------------------------------------+
void ExecuteMartingaleStrategy()
{
   // 更新网格级别
   UpdateGridLevel();

   switch(TradeDirection)
   {
      case 0: // 仅买入
         ExecuteBuyStrategy();
         break;

      case 1: // 仅卖出
         ExecuteSellStrategy();
         break;

      case 2: // 双向交易
         // 在双向模式下，优先处理现有方向的加仓
         int orderCount = CountOrdersByMagic();
         if(orderCount == 0)
         {
            // 没有订单时，根据市场条件选择方向
            if(CheckEntry())
            {
               // 简单的方向选择逻辑（可以改进）
               static int lastDirection = OP_BUY;
               lastDirection = (lastDirection == OP_BUY) ? OP_SELL : OP_BUY;

               if(lastDirection == OP_BUY)
               {
                  ExecuteBuyStrategy();
               }
               else
               {
                  ExecuteSellStrategy();
               }
            }
         }
         else
         {
            // 有订单时，检查加仓条件
            ExecuteBuyStrategy();
            ExecuteSellStrategy();
         }
         break;
   }
}

//+------------------------------------------------------------------+
//| 风险控制模块 (Risk Control Module)                                |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 实现CheckRisk()函数，评估当前持仓的风险水平                      |
//+------------------------------------------------------------------+
bool CheckRisk()
{
   Print("=== 开始风险评估 ===");

   bool riskAcceptable = true;

   // 检查账户基本信息
   double balance = AccountBalance();
   double equity = AccountEquity();
   double freeMargin = AccountFreeMargin();
   double usedMargin = AccountMargin();

   Print("账户信息:");
   Print("- 余额: ", balance);
   Print("- 净值: ", equity);
   Print("- 可用保证金: ", freeMargin);
   Print("- 已用保证金: ", usedMargin);

   // 1. 检查净值与余额比例（回撤检查）
   double drawdownPercent = 0;
   if(balance > 0)
   {
      drawdownPercent = ((balance - equity) / balance) * 100;
   }

   Print("当前回撤: ", drawdownPercent, "%");

   if(drawdownPercent > 30) // 回撤超过30%
   {
      Print("警告: 回撤过大 ", drawdownPercent, "%");
      riskAcceptable = false;
   }

   // 2. 检查保证金水平
   double marginLevel = 0;
   if(usedMargin > 0)
   {
      marginLevel = (equity / usedMargin) * 100;
   }

   Print("保证金水平: ", marginLevel, "%");

   if(marginLevel < 200 && marginLevel > 0) // 保证金水平低于200%
   {
      Print("警告: 保证金水平过低 ", marginLevel, "%");
      riskAcceptable = false;
   }

   // 3. 检查当前订单数量
   int orderCount = CountOrdersByMagic();
   Print("当前订单数: ", orderCount, "/", MaxOrders);

   if(orderCount >= MaxOrders)
   {
      Print("警告: 已达到最大订单数限制");
      riskAcceptable = false;
   }

   // 4. 检查总浮动盈亏
   double totalProfit = CalculateTotalProfit();
   double profitPercent = 0;
   if(balance > 0)
   {
      profitPercent = (totalProfit / balance) * 100;
   }

   Print("总浮动盈亏: ", totalProfit, " (", profitPercent, "%)");

   if(profitPercent < -20) // 浮亏超过20%
   {
      Print("警告: 浮动亏损过大 ", profitPercent, "%");
   }

   Print("风险评估结果: ", riskAcceptable ? "可接受" : "风险过高");

   return riskAcceptable;
}

//+------------------------------------------------------------------+
//| 计算总浮动盈亏                                                      |
//+------------------------------------------------------------------+
double CalculateTotalProfit()
{
   double totalProfit = 0;
   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   for(int i = 0; i < count; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
      }
   }

   return totalProfit;
}

//+------------------------------------------------------------------+
//| 2. 实现SetStopLoss()函数，为订单设置止损价格                       |
//+------------------------------------------------------------------+
bool SetStopLoss(int ticket, double stopLossPrice)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA");
      return false;
   }

   double currentSL = OrderStopLoss();
   double currentTP = OrderTakeProfit();
   double openPrice = OrderOpenPrice();
   int orderType = OrderType();

   // 标准化止损价格
   stopLossPrice = NormalizeDouble(stopLossPrice, Digits);

   // 验证止损价格的合理性
   double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

   if(orderType == OP_BUY)
   {
      if(stopLossPrice >= Bid - minDistance)
      {
         Print("错误: 买单止损价格过高 SL=", stopLossPrice, " Bid=", Bid);
         return false;
      }
   }
   else if(orderType == OP_SELL)
   {
      if(stopLossPrice <= Ask + minDistance)
      {
         Print("错误: 卖单止损价格过低 SL=", stopLossPrice, " Ask=", Ask);
         return false;
      }
   }

   // 如果止损价格没有变化，不需要修改
   if(MathAbs(stopLossPrice - currentSL) < Point)
   {
      return true;
   }

   // 修改订单
   if(ModifyOrder(ticket, openPrice, stopLossPrice, currentTP))
   {
      Print("止损设置成功: Ticket=", ticket, " SL=", stopLossPrice);
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 3. 实现SetTakeProfit()函数，为订单设置止盈价格                     |
//+------------------------------------------------------------------+
bool SetTakeProfit(int ticket, double takeProfitPrice)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA");
      return false;
   }

   double currentSL = OrderStopLoss();
   double currentTP = OrderTakeProfit();
   double openPrice = OrderOpenPrice();
   int orderType = OrderType();

   // 标准化止盈价格
   takeProfitPrice = NormalizeDouble(takeProfitPrice, Digits);

   // 验证止盈价格的合理性
   double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

   if(orderType == OP_BUY)
   {
      if(takeProfitPrice <= Ask + minDistance)
      {
         Print("错误: 买单止盈价格过低 TP=", takeProfitPrice, " Ask=", Ask);
         return false;
      }
   }
   else if(orderType == OP_SELL)
   {
      if(takeProfitPrice >= Bid - minDistance)
      {
         Print("错误: 卖单止盈价格过高 TP=", takeProfitPrice, " Bid=", Bid);
         return false;
      }
   }

   // 如果止盈价格没有变化，不需要修改
   if(MathAbs(takeProfitPrice - currentTP) < Point)
   {
      return true;
   }

   // 修改订单
   if(ModifyOrder(ticket, openPrice, currentSL, takeProfitPrice))
   {
      Print("止盈设置成功: Ticket=", ticket, " TP=", takeProfitPrice);
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 4. 实现MaxOrdersCheck()函数，检查是否超过最大订单数限制             |
//+------------------------------------------------------------------+
bool MaxOrdersCheck()
{
   int currentOrders = CountOrdersByMagic();

   if(currentOrders >= MaxOrders)
   {
      Print("已达到最大订单数限制: ", currentOrders, "/", MaxOrders);
      return false;
   }

   Print("订单数检查通过: ", currentOrders, "/", MaxOrders);
   return true;
}

//+------------------------------------------------------------------+
//| 5. 添加紧急平仓功能，在极端情况下强制关闭所有订单                    |
//+------------------------------------------------------------------+
bool EmergencyCloseAll(string reason = "Emergency")
{
   Print("=== 紧急平仓触发 ===");
   Print("原因: ", reason);

   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   if(count == 0)
   {
      Print("没有需要关闭的订单");
      return true;
   }

   Print("开始紧急关闭 ", count, " 个订单");

   bool allClosed = true;
   int closedCount = 0;

   for(int i = 0; i < count; i++)
   {
      if(CloseOrder(tickets[i]))
      {
         closedCount++;
         Print("订单 ", tickets[i], " 关闭成功");
      }
      else
      {
         allClosed = false;
         Print("订单 ", tickets[i], " 关闭失败");
      }

      Sleep(100); // 短暂延迟避免过于频繁的请求
   }

   Print("紧急平仓完成: ", closedCount, "/", count, " 个订单已关闭");

   // 禁用EA交易
   if(!allClosed)
   {
      g_IsTradeAllowed = false;
      Print("部分订单关闭失败，已禁用EA交易");
   }

   return allClosed;
}

//+------------------------------------------------------------------+
//| 6. 实现资金保护机制，防止账户资金过度损失                            |
//+------------------------------------------------------------------+
bool FundProtectionCheck()
{
   double balance = AccountBalance();
   double equity = AccountEquity();

   if(balance <= 0)
   {
      Print("错误: 账户余额异常");
      return false;
   }

   // 计算当前回撤百分比
   double drawdownPercent = ((balance - equity) / balance) * 100;

   // 设置最大允许回撤（可以通过外部参数配置）
   double maxDrawdownPercent = 50.0; // 最大回撤50%

   if(drawdownPercent >= maxDrawdownPercent)
   {
      Print("资金保护触发: 回撤 ", drawdownPercent, "% >= ", maxDrawdownPercent, "%");
      EmergencyCloseAll("资金保护 - 回撤过大");
      return false;
   }

   // 检查最小账户净值
   double minEquityPercent = 30.0; // 最小净值为余额的30%
   double minEquity = balance * (minEquityPercent / 100);

   if(equity <= minEquity)
   {
      Print("资金保护触发: 净值 ", equity, " <= 最小净值 ", minEquity);
      EmergencyCloseAll("资金保护 - 净值过低");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| 7. 添加保证金检查，确保有足够保证金支持新订单                        |
//+------------------------------------------------------------------+
bool MarginCheck(double lots)
{
   double freeMargin = AccountFreeMargin();
   double requiredMargin = MarketInfo(Symbol(), MODE_MARGINREQUIRED) * lots;

   // 保留安全边际（2倍保证金）
   double safetyMargin = requiredMargin * 2;

   if(freeMargin < safetyMargin)
   {
      Print("保证金不足: 可用=", freeMargin, " 需要=", safetyMargin, " (含安全边际)");
      return false;
   }

   // 检查保证金水平
   double usedMargin = AccountMargin();
   double equity = AccountEquity();

   if(usedMargin > 0)
   {
      double marginLevel = (equity / (usedMargin + requiredMargin)) * 100;

      if(marginLevel < 150) // 保证金水平低于150%
      {
         Print("保证金水平过低: ", marginLevel, "%");
         return false;
      }
   }

   Print("保证金检查通过: 可用=", freeMargin, " 需要=", requiredMargin);
   return true;
}

//+------------------------------------------------------------------+
//| 综合风险检查函数                                                    |
//+------------------------------------------------------------------+
bool ComprehensiveRiskCheck(double lots = 0)
{
   // 1. 基础风险检查
   if(!CheckRisk())
   {
      return false;
   }

   // 2. 资金保护检查
   if(!FundProtectionCheck())
   {
      return false;
   }

   // 3. 最大订单数检查
   if(!MaxOrdersCheck())
   {
      return false;
   }

   // 4. 保证金检查（如果指定了手数）
   if(lots > 0 && !MarginCheck(lots))
   {
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| 设置订单的止损止盈                                                  |
//+------------------------------------------------------------------+
bool SetOrderStopLossAndTakeProfit(int ticket)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      return false;
   }

   double openPrice = OrderOpenPrice();
   int orderType = OrderType();
   double stopLoss = 0;
   double takeProfit = 0;

   // 计算止损止盈价格
   if(orderType == OP_BUY)
   {
      if(StopLoss > 0)
         stopLoss = openPrice - StopLoss * Point;
      if(TakeProfit > 0)
         takeProfit = openPrice + TakeProfit * Point;
   }
   else if(orderType == OP_SELL)
   {
      if(StopLoss > 0)
         stopLoss = openPrice + StopLoss * Point;
      if(TakeProfit > 0)
         takeProfit = openPrice - TakeProfit * Point;
   }

   bool success = true;

   // 设置止损
   if(stopLoss > 0 && !SetStopLoss(ticket, stopLoss))
   {
      success = false;
   }

   // 设置止盈
   if(takeProfit > 0 && !SetTakeProfit(ticket, takeProfit))
   {
      success = false;
   }

   return success;
}

//+------------------------------------------------------------------+
//| 辅助函数: 检查交易时间                                              |
//+------------------------------------------------------------------+
bool IsTradeTime()
{
   // 简单的交易时间检查
   int currentHour = Hour();

   // 避免在市场关闭时间交易（周末）
   int dayOfWeek = DayOfWeek();
   if(dayOfWeek == 0 || dayOfWeek == 6) // 周日或周六
   {
      return false;
   }

   // 避免在重要新闻时间交易（可以根据需要调整）
   // 这里只是示例，实际使用时可以添加更复杂的逻辑

   return true;
}

//+------------------------------------------------------------------+
//| 辅助函数: 获取EA状态信息                                            |
//+------------------------------------------------------------------+
string GetEAStatus()
{
   string status = "EA状态: ";

   if(!g_IsInitialized)
      status += "未初始化";
   else if(!g_IsTradeAllowed)
      status += "交易禁用";
   else if(!IsTradeTime())
      status += "非交易时间";
   else
      status += "正常运行";

   return status;
}

//+------------------------------------------------------------------+
//| 程序结束                                                          |
//+------------------------------------------------------------------+
