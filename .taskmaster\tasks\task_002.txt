# Task ID: 2
# Title: 外部参数定义和验证 (External Parameters Definition)
# Status: pending
# Dependencies: None
# Priority: high
# Description: 定义所有可配置的外部参数，包括初始手数、加仓倍数、最大订单数等，并实现参数验证逻辑
# Details:
1. 使用extern关键字定义Lots(0.01)初始手数参数
2. 定义Multiplier(2.0)加仓倍数参数
3. 定义MaxOrders(7)最大订单数参数
4. 定义GridStep(30)加仓间距参数
5. 定义TakeProfit(50)和StopLoss(200)止盈止损参数
6. 定义TradeDirection(2)交易方向参数
7. 定义MagicNumber(123456)魔术号码参数
8. 添加参数范围验证逻辑，确保参数值在合理范围内

# Test Strategy:
测试各种参数组合，验证边界值和异常值处理
